codeunit 60031 "Item Price Dataset JQ IPK"
{
    TableNo = "Job Queue Entry";

    trigger OnRun()
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMs: BigInteger;
        ProcessingTimeMinutes: Decimal;
        StartDate: Date;
        ProductionItemCount: Integer;
        PurchaseItemCount: Integer;
    begin
        // Record start time for performance monitoring
        StartTime := CurrentDateTime();

        // Step 1: Get configuration from setup
        IpekPamukSetup.GetRecordOnce();
        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
        if StartDate = 0D then
            Error(StartDateNotConfiguredErr);

        // Step 2: Clear all existing records from ItemPriceDatasetIPK table
        ClearAllExistingRecords();

        // Step 3: Process Production Order items using existing proven logic
        ProductionItemCount := ProcessProductionOrderItems();

        // Step 4: Process Purchase items using existing proven logic
        PurchaseItemCount := ProcessPurchaseItems();

        // Record end time and calculate processing duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Log successful completion
        LogJobQueueSuccess(ProductionItemCount, PurchaseItemCount, ProcessingTimeMinutes);
    end;

    /// <summary>
    /// Clears all existing records from the Item Price Dataset table.
    /// Uses SetLoadFields for optimal performance.
    /// </summary>
    local procedure ClearAllExistingRecords()
    var
        ItemPriceDataset: Record "Item Price Dataset IPK";
        RecordCount: Integer;
    begin
        // Use SetLoadFields to optimize performance - only load primary key for deletion
        ItemPriceDataset.SetLoadFields("Entry No.");

        if not ItemPriceDataset.IsEmpty() then begin
            RecordCount := ItemPriceDataset.Count();
            ItemPriceDataset.DeleteAll(true);

            // Log the cleanup operation
            LogMessage(StrSubstNo(RecordsClearedMsg, RecordCount));
        end else
            LogMessage(NoRecordsToClearMsg);
    end;

    /// <summary>
    /// Processes all items with Replenishment System = Production Order.
    /// Delegates to existing management codeunit and returns count of items processed.
    /// </summary>
    /// <returns>Number of production items processed</returns>
    local procedure ProcessProductionOrderItems(): Integer
    var
        Item: Record Item;
        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
        ItemCount: Integer;
    begin
        // Count production items before processing
        Item.SetLoadFields("No.");
        Item.SetRange("Replenishment System", Item."Replenishment System"::"Prod. Order");
        ItemCount := Item.Count();

        // Delegate to existing proven logic in management codeunit
        ItemPriceDatasetMgt.ProcessAllProductionItems();

        // Log production processing results
        LogMessage(StrSubstNo(ProductionItemsProcessedMsg, ItemCount));

        exit(ItemCount);
    end;

    /// <summary>
    /// Processes all items with Replenishment System = Purchase.
    /// Delegates to existing management codeunit and returns count of items processed.
    /// </summary>
    /// <returns>Number of purchase items processed</returns>
    local procedure ProcessPurchaseItems(): Integer
    var
        Item: Record Item;
        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
        ItemCount: Integer;
    begin
        // Count purchase items before processing
        Item.SetLoadFields("No.");
        Item.SetRange("Replenishment System", Item."Replenishment System"::Purchase);
        ItemCount := Item.Count();

        // Delegate to existing proven logic in management codeunit
        ItemPriceDatasetMgt.ProcessAllPurchaseItems();

        // Log purchase processing results
        LogMessage(StrSubstNo(PurchaseItemsProcessedMsg, ItemCount));

        exit(ItemCount);
    end;



    /// <summary>
    /// Logs successful job completion with processing statistics.
    /// </summary>
    /// <param name="ProductionItemCount">Number of production items processed</param>
    /// <param name="PurchaseItemCount">Number of purchase items processed</param>
    /// <param name="ProcessingTimeMinutes">Total processing time in minutes</param>
    local procedure LogJobQueueSuccess(ProductionItemCount: Integer; PurchaseItemCount: Integer; ProcessingTimeMinutes: Decimal)
    begin
        LogMessage(StrSubstNo(JobQueueCompletedSuccessfullyMsg,
            ProductionItemCount, PurchaseItemCount, Round(ProcessingTimeMinutes, 0.01)));
    end;



    /// <summary>
    /// Generic logging procedure for job queue monitoring.
    /// Can be extended to write to event log or custom logging table.
    /// </summary>
    /// <param name="LogText">Message to log</param>
    local procedure LogMessage(LogText: Text)
    begin
        // For now, this is a placeholder for logging
        // In production, this could write to:
        // - Windows Event Log
        // - Custom logging table
        // - Application Insights
        // - File system log

        // Currently just available for debugging in development
        // Message(LogText);
    end;

    var
        StartDateNotConfiguredErr: Label 'Item Price Dataset Start Date is not configured in Ipek Pamuk Setup. Please configure the start date before running the job queue.';
        RecordsClearedMsg: Label 'Cleared %1 existing records from Item Price Dataset table.', Comment = '%1 = Number of records cleared';
        NoRecordsToClearMsg: Label 'No existing records found in Item Price Dataset table to clear.';
        ProductionItemsProcessedMsg: Label 'Production items processing completed: %1 items processed.', Comment = '%1 = Item count';
        PurchaseItemsProcessedMsg: Label 'Purchase items processing completed: %1 items processed.', Comment = '%1 = Item count';
        JobQueueCompletedSuccessfullyMsg: Label 'Item Price Dataset refresh completed successfully. Production items: %1, Purchase items: %2, Processing time: %3 minutes.', Comment = '%1 = Production item count, %2 = Purchase item count, %3 = Processing time';
}
