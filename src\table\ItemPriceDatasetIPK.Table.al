table 60030 "Item Price Dataset IPK"
{
    Caption = 'Item Price Dataset';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Price Dataset List IPK";
    LookupPageId = "Item Price Dataset List IPK";

    fields
    {
        field(1; "Entry No.";
        Integer)
        {
            Caption = 'Entry No.';
            AutoIncrement = true;
            ToolTip = 'Specifies the unique entry number for this record.';
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the item number.';

            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get(Rec."Item No.") then begin
                    Rec.Description := Item.Description;
                    Rec."Base Unit of Measure Code" := Item."Base Unit of Measure";
                end else begin
                    Rec.Description := '';
                    Rec."Base Unit of Measure Code" := '';
                end;
            end;
        }
        field(3; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            //TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the variant code for the item.';

            trigger OnValidate()
            var
                ItemVariant: Record "Item Variant";
            begin
                if (Rec."Variant Code" <> '') and ItemVariant.Get(Rec."Item No.", Rec."Variant Code") then
                    Rec.Description := ItemVariant.Description
                else
                    if Rec."Item No." <> '' then
                        Rec.Validate("Item No.");
            end;
        }
        field(4; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the description of the item or item variant.';
        }
        field(5; "Source Price Month"; Text[7])
        {
            Caption = 'Source Price Month';
            ToolTip = 'Specifies the reference month for price calculation in MM.YYYY format (e.g., 01.2025).';
        }
        field(6; "Average Unit Price"; Decimal)
        {
            Caption = 'Unit Price/Cost (ACY)';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the calculated average unit price/cost for the item in Additional Currency (ACY).';
        }
        field(13; "Manual Unit Price/Cost"; Decimal)
        {
            Caption = 'Manual Unit Price/Cost';
            DecimalPlaces = 2 : 5;
            ToolTip = 'Specifies the manually entered unit price/cost for the item.';
        }
        // field(7; "Source Item Ledger Entries"; Integer)
        // {
        //     Caption = 'Source Item Ledger Entries';
        //     FieldClass = FlowField;
        //     CalcFormula = count("Item Ledger Entry" where("Item No." = field("Item No."),
        //                                                  "Variant Code" = field("Variant Code"),
        //                                                  "Posting Date" = field("Start Date".."End Date"),
        //                                                  "Entry Type" = field("Entry Type")));
        //     Editable = false;
        //     ToolTip = 'Specifies the number of item ledger entries used for price calculation.';
        // }
        field(8; "Start Date"; Date)
        {
            Caption = 'Start Date';
            ToolTip = 'Specifies the start date of the period for price calculation.';

            trigger OnValidate()
            begin
                if ("Start Date" <> 0D) and ("End Date" <> 0D) and ("Start Date" > "End Date") then
                    Error(StartDateLaterThanEndDateErr);
            end;
        }
        field(9; "End Date"; Date)
        {
            Caption = 'End Date';
            ToolTip = 'Specifies the end date of the period for price calculation.';

            trigger OnValidate()
            begin
                if ("Start Date" <> 0D) and ("End Date" <> 0D) and ("Start Date" > "End Date") then
                    Error(EndDateEarlierThanStartDateErr);
            end;
        }
        field(10; "Entry Type"; Enum "Item Ledger Entry Type")
        {
            Caption = 'Entry Type';
            ToolTip = 'Specifies the type of item ledger entries to include in the calculation.';
        }
        field(11; "Base Unit of Measure Code"; Code[10])
        {
            Caption = 'Base Unit of Measure Code';
            TableRelation = "Unit of Measure".Code;
            ToolTip = 'Specifies the base unit of measure code for the item.';
        }
        field(12; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the currency code for the Additional Currency (ACY) amounts.';
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(ItemKey; "Item No.", "Variant Code", "Source Price Month")
        {
        }
        key(DateKey; "Start Date", "End Date")
        {
        }
    }

    fieldgroups
    {
        fieldgroup(DropDown; "Entry No.", "Item No.", "Variant Code", Description, "Base Unit of Measure Code", "Average Unit Price", "Currency Code")
        {
        }
        fieldgroup(Brick; "Item No.", Description, "Base Unit of Measure Code", "Average Unit Price", "Source Price Month", "Currency Code")
        {
        }
    }

    trigger OnInsert()
    begin
        if "Source Price Month" = '' then
            "Source Price Month" := Format(WorkDate(), 0, '<Month,2>.<Year4>');
    end;

    trigger OnModify()
    begin
        // Recalculate FlowField when key fields change
        //CalcFields("Source Item Ledger Entries");
    end;

    var
        StartDateLaterThanEndDateErr: Label 'Start Date cannot be later than End Date.';
        EndDateEarlierThanStartDateErr: Label 'End Date cannot be earlier than Start Date.';
}
