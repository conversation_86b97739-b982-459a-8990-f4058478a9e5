page 60032 "Item Price Dataset List IPK"
{
    ApplicationArea = All;
    Caption = 'Item Price Dataset';
    PageType = List;
    SourceTable = "Item Price Dataset IPK";
    UsageCategory = Lists;
    Editable = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Base Unit of Measure Code"; Rec."Base Unit of Measure Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Source Price Month"; Rec."Source Price Month")
                {
                }
                field("Average Unit Price"; Rec."Average Unit Price")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Start Date"; Rec."Start Date")
                {
                }
                field("End Date"; Rec."End Date")
                {
                }
                field("Entry Type"; Rec."Entry Type")
                {
                }
                field("Manual Unit Price/Cost"; Rec."Manual Unit Price/Cost")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group(DataProcessing)
            {
                Caption = 'Data Processing';

                action(ProcessPriceDataset)
                {
                    ApplicationArea = All;
                    Caption = 'Process Price Dataset';
                    Image = Calculate;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    Visible = false;
                    ToolTip = 'Intelligently processes Item Ledger Entries and creates/updates monthly price dataset records for a selected item based on its Replenishment System.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
                        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
                        Item: Record Item;
                        ItemNo: Code[20];
                        StartDate: Date;
                    begin
                        // Get setup values
                        IpekPamukSetup.GetRecordOnce();
                        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
                        if StartDate = 0D then
                            Error('Please specify Item Price Dataset Start Date in Ipek Pamuk Setup.');

                        // Request item selection
                        if not RequestItemSelection(ItemNo) then
                            exit;

                        // Get the selected item to check its Replenishment System
                        if not Item.Get(ItemNo) then
                            Error('Item %1 does not exist.', ItemNo);

                        // Process based on Replenishment System
                        ItemPriceDatasetMgt.ProcessPriceDatasetFromItemIntelligent(ItemNo, StartDate, Item."Replenishment System");
                    end;
                }
                action(RecalculateAveragePrices)
                {
                    ApplicationArea = All;
                    Caption = 'Recalculate Average Prices';
                    Image = Refresh;
                    Promoted = true;
                    PromotedOnly = true;
                    Visible = false;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Recalculates average prices for existing records in the dataset.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
                    begin
                        if Confirm('This will recalculate average prices for all existing sales records. Continue?') then
                            ItemPriceDatasetMgt.RecalculateAveragePrices();
                    end;
                }
                action(FillPriceGaps)
                {
                    ApplicationArea = All;
                    Caption = 'Fill Price Gaps';
                    Image = Process;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    Visible = false;
                    ToolTip = 'Automatically fills missing months between earliest and latest records, and extends to current month, for each item/variant combination with interpolated prices.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
                    begin
                        if Confirm('This will create interpolated price records for missing months between actual invoice data. Continue?') then
                            ItemPriceDatasetMgt.FillAllPriceDatasetGaps();
                    end;
                }
                action(ProcessAllProductionItems)
                {
                    ApplicationArea = All;
                    Caption = 'Process All Production Items';
                    Image = ItemGroup;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Process sales price dataset for all items with Replenishment System = Prod. Order and show processing time.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
                    begin
                        if Confirm('This will process sales price dataset for all items with Replenishment System = Prod. Order. Continue?') then
                            ItemPriceDatasetMgt.ProcessAllProductionItems();
                    end;
                }
                action(ProcessAllPurchaseItems)
                {
                    ApplicationArea = All;
                    Caption = 'Process All Purchase Items';
                    Image = Purchase;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    ToolTip = 'Process purchase price dataset for all items with Replenishment System = Purchase and show processing time.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMgt: Codeunit "Item Price Dataset Mgt IPK";
                    begin
                        if Confirm('This will process purchase price dataset for all items with Replenishment System = Purchase. Continue?') then
                            ItemPriceDatasetMgt.ProcessAllPurchaseItems();
                    end;
                }
            }
            group(Related)
            {
                Caption = 'Related';

                action(ShowValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Sales)';
                    Image = ValueLedger;
                    Promoted = true;
                    PromotedCategory = Category4;
                    PromotedIsBig = true;
                    ToolTip = 'View the value entries (sales invoices and credit memos) that were used to calculate the average price for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error('No item selected.');

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
                        ValueEntry.SetFilter("Document Type", '%1|%2',
                            ValueEntry."Document Type"::"Sales Invoice",
                            ValueEntry."Document Type"::"Sales Credit Memo");
                        ValueEntry.SetFilter("Sales Amount (Actual)", '<>0');
                        ValueEntry.SetFilter("Invoiced Quantity", '<>0');

                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
                action(ShowPurchaseValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Purchase)';
                    Image = Purchase;
                    Promoted = true;
                    PromotedCategory = Category4;
                    PromotedIsBig = true;
                    ToolTip = 'View the value entries (purchase invoices and credit memos) that were used to calculate the average cost for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error('No item selected.');

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Purchase);
                        ValueEntry.SetFilter("Document Type", '%1|%2',
                            ValueEntry."Document Type"::"Purchase Invoice",
                            ValueEntry."Document Type"::"Purchase Credit Memo");
                        ValueEntry.SetFilter("Cost Amount (Actual)", '<>0');
                        ValueEntry.SetFilter("Invoiced Quantity", '<>0');

                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
            }
        }
    }

    local procedure RequestItemSelection(var ItemNo: Code[20]): Boolean
    var
        Item: Record Item;
        ItemList: Page "Item List";
    begin
        ItemList.LookupMode(true);
        if ItemList.RunModal() = Action::LookupOK then begin
            ItemList.GetRecord(Item);
            ItemNo := Item."No.";
            exit(true);
        end;
        exit(false);
    end;
}
