tableextension 60014 "Production Order IPK" extends "Production Order"
{
    fields
    {
        field(60000; "Source Document No. IPK"; Code[20])
        {
            Caption = 'Source Document No.';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Source Document No. field.';
            trigger OnLookup()
            var
                SalesHeader: Record "Sales Header";
            begin
                SalesHeader.Get(Enum::"Sales Document Type"::Order, Rec."Source Document No. IPK");
                PageManagement.PageRun(SalesHeader);
            end;
        }
        field(60001; "Source Document Line No. IPK"; Integer)
        {
            AllowInCustomizations = Always;
            Caption = 'Source Document Line No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Document Line No. field.';

            trigger OnLookup()
            var
                SalesHeader: Record "Sales Header";
            begin
                SalesHeader.Get(Enum::"Sales Document Type"::Order, Rec."Source Document No. IPK");
                PageManagement.PageRun(SalesHeader);
            end;
        }
        field(60002; "Consumption Location Code IPK"; Code[50])
        {
            Caption = 'Consumption Location Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Prod. Order Component"."Location Code" where("Prod. Order No." = field("No."), Status = field(Status)));
            ToolTip = 'Specifies the value of the Consumption Location Code field.';
        }
        field(60003; "Remaining Quantity IPK"; Decimal)
        {
            Caption = 'Remaining Quantity';
            Editable = false;
            AllowInCustomizations = Always;
            FieldClass = FlowField;
            CalcFormula = sum("Prod. Order Line"."Remaining Quantity" where("Prod. Order No." = field("No."), Status = field(Status)));
            ToolTip = 'Specifies the value of the Remaining Quantity field.';
        }
        field(60004; "Customer Name IPK"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Sell-to Customer Name" where("Document Type" = const(Order), "No." = field("Source Document No. IPK")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(60005; "Last Production Date-Time IPK"; DateTime)
        {
            Caption = 'Last Production Date-Time';
            Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = lookup("Sales Header"."Sell-to Customer Name" where("Document Type" = const(Order), "No." = field("Source Document No. IPK")));
            ToolTip = 'Specifies the value of the Last Production Date-Time field.';
        }
        field(60006; "Package Count IPK"; Integer)
        {
            Caption = 'Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Prod. Order Line Detail IPK" where("Prod. Order No." = field("No.")));
            ToolTip = 'Specifies the value of the Package Count field.';
        }
        modify("Source No.")
        {
            trigger OnBeforeValidate()
            var
                Family: Record Family;
            begin
                Rec."Source Type" := Rec."Source Type"::Item;
                if Family.Get(Rec."Source No.") then
                    Rec.Validate("Source Type", Rec."Source Type"::Family)
                else
                    Rec.Validate("Source Type", Rec."Source Type"::Item);
            end;

        }
    }
    var
        PageManagement: Codeunit "Page Management";
}