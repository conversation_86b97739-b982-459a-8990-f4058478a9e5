codeunit 60030 "Item Price Dataset Mgt IPK"
{
    var
        ConfirmManagement: Codeunit "Confirm Management";
        StartDateMustBeSpecifiedErr: Label 'Start Date must be specified.';
        EndDateMustBeSpecifiedErr: Label 'End Date must be specified.';
        StartDateLaterThanEndDateErr: Label 'Start Date cannot be later than End Date.';
        YearRangeErr: Label 'Year must be between 1900 and 2100.';
        MonthRangeErr: Label 'Month must be between 1 and 12.';
        OperationCancelledErr: Label 'Operation cancelled by user.';
        RecalculationCompletedMsg: Label 'Recalculation completed. %1 records updated.', Comment = '%1 = Number of records updated';
        DeleteExistingRecordsQst: Label 'Existing sales data records found for the specified period. Do you want to delete them and recalculate?';
        ItemNoMustBeSpecifiedErr: Label 'Item No. must be specified.';
        ItemNotFoundErr: Label 'Item %1 does not exist.', Comment = '%1 = Item number';
        SalesDataPopulationCompletedWithDetailsMsg: Label 'Sales data population completed. %1 monthly records created, %2 records populated with actual invoice and credit memo data.', Comment = '%1 = Number of monthly records created, %2 = Number of records populated';
        GapFillingCompletedForAllItemsMsg: Label 'Gap filling completed for all items in the dataset.';
        TestCompletedForItemMsg: Label 'Test completed for item MK000064. %1 monthly records created, %2 records populated with actual invoice and credit memo data.', Comment = '%1 = Number of monthly records created, %2 = Number of records populated';
        SalesProcessingCompletedMsg: Label 'Sales processing completed for %1 production items. %2 monthly records created, %3 records populated with actual invoice and credit memo data. Processing time: %4 minutes.', Comment = '%1 = Number of items, %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';
        PurchaseProcessingCompletedMsg: Label 'Purchase processing completed for %1 purchase items. %2 monthly records created, %3 records populated with actual purchase invoice and credit memo data. Processing time: %4 minutes.', Comment = '%1 = Number of items, %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';
        IntelligentProcessingCompletedMsg: Label '%1 completed for item %2. %3 monthly records created, %4 records populated with actual data.', Comment = '%1 = Processing type, %2 = Item number, %3 = Number of monthly records, %4 = Number of populated records';
        PriceDatasetProcessingCompletedForItemMsg: Label 'Price dataset processing completed for item %1. %2 monthly records created, %3 records populated with actual invoice and credit memo data.', Comment = '%1 = Item number, %2 = Number of monthly records, %3 = Number of populated records';

    /// <summary>
    /// Populates the Item Price Dataset table with sales data for a specified date range.
    /// Processes Value Entry records with Document Type = Sales Invoice and Sales Credit Memo and calculates weighted average unit sales prices based on invoice dates.
    /// </summary>
    /// <param name="StartDate">Start date of the period to process (based on invoice dates)</param>
    /// <param name="EndDate">End date of the period to process (based on invoice dates)</param>
    procedure PopulateSalesData(StartDate: Date; EndDate: Date)
    var
        Item: Record Item;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
    begin
        // Validate input parameters
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);
        if EndDate = 0D then
            Error(EndDateMustBeSpecifiedErr);
        if StartDate > EndDate then
            Error(StartDateLaterThanEndDateErr);

        // Clear existing records for the same period
        ClearExistingRecords(StartDate, EndDate);

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;

        // Process all items using the working structure
        if Item.FindSet() then
            repeat
                // Step 1: Create monthly structure for this item
                CreatedRecords := CreateCompleteMonthlyStructure(Item."No.");
                TotalCreated += CreatedRecords;

                // Step 2: Populate prices for this item
                PopulatedRecords := PopulatePricesFromInvoicesPublic(Item."No.", StartDate);
                TotalPopulated += PopulatedRecords;
            until Item.Next() = 0;

        Message(SalesDataPopulationCompletedWithDetailsMsg,
            TotalCreated, TotalPopulated);
    end;



    local procedure CreateFinalRecordsFromDictionaries(SalesDataDict: Dictionary of [Text, Decimal]; QuantityDataDict: Dictionary of [Text, Decimal]; MonthStartDate: Date; MonthEndDate: Date; var ProcessedRecords: Integer)
    var
        FinalRecord: Record "Item Price Dataset IPK";
        SalesDataKey: Text;
        KeyParts: List of [Text];
        TotalSalesAmount: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgPrice: Decimal;
        ItemNo: Code[20];
        VariantCode: Code[10];
    begin
        foreach SalesDataKey in SalesDataDict.Keys() do begin
            // Parse the key to extract Item No. and Variant Code
            KeyParts := SalesDataKey.Split('|');
            if KeyParts.Count() >= 2 then begin
                ItemNo := CopyStr(KeyParts.Get(1), 1, 20);
                VariantCode := CopyStr(KeyParts.Get(2), 1, 10);
            end else
                continue; // Skip invalid keys

            // Get totals from dictionaries
            TotalSalesAmount := SalesDataDict.Get(SalesDataKey);
            TotalQuantity := QuantityDataDict.Get(SalesDataKey);

            // Calculate weighted average price
            if TotalQuantity <> 0 then
                WeightedAvgPrice := TotalSalesAmount / TotalQuantity
            else
                WeightedAvgPrice := 0;

            // Create final record
            FinalRecord.Init();
            FinalRecord."Item No." := ItemNo;
            FinalRecord.Validate("Item No."); // This will populate Description and Base Unit of Measure Code
            FinalRecord."Variant Code" := VariantCode;
            if FinalRecord."Variant Code" <> '' then
                FinalRecord.Validate("Variant Code"); // This will update Description if variant exists
            FinalRecord."Source Price Month" := Format(MonthStartDate, 0, '<Month,2>.<Year4>');
            FinalRecord."Average Unit Price" := WeightedAvgPrice;
            FinalRecord."Start Date" := MonthStartDate;
            FinalRecord."End Date" := MonthEndDate;
            FinalRecord."Entry Type" := FinalRecord."Entry Type"::Sale;

            if FinalRecord.Insert(true) then
                ProcessedRecords += 1;
        end;
    end;

    local procedure ClearExistingRecords(StartDate: Date; EndDate: Date)
    var
        ExistingRecord: Record "Item Price Dataset IPK";
    begin
        // Clear existing records that overlap with the specified date range
        ExistingRecord.SetFilter("Start Date", '<=%1', EndDate);
        ExistingRecord.SetFilter("End Date", '>=%1', StartDate);
        ExistingRecord.SetRange("Entry Type", ExistingRecord."Entry Type"::Sale);

        if not ExistingRecord.IsEmpty() then
            if ConfirmManagement.GetResponseOrDefault(DeleteExistingRecordsQst, true) then
                ExistingRecord.DeleteAll(true)
            else
                Error(OperationCancelledErr);
    end;

    /// <summary>
    /// Populates sales data for a specific month (convenience method).
    /// </summary>
    /// <param name="Year">Year to process</param>
    /// <param name="Month">Month to process (1-12)</param>
    procedure PopulateSalesDataForMonth(Year: Integer; Month: Integer)
    var
        StartDate: Date;
        EndDate: Date;
    begin
        if (Year < 1900) or (Year > 2100) then
            Error(YearRangeErr);
        if (Month < 1) or (Month > 12) then
            Error(MonthRangeErr);

        StartDate := DMY2Date(1, Month, Year);
        EndDate := CalcDate('<CM>', StartDate);

        PopulateSalesData(StartDate, EndDate);
    end;

    /// <summary>
    /// Recalculates average prices for existing records in the dataset.
    /// </summary>
    procedure RecalculateAveragePrices()
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        RecalculatedCount: Integer;
    begin
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);

        if DatasetRecord.FindSet() then
            repeat
                if DatasetRecord."Manual Unit Price/Cost" <> 0 then
                    continue;

                if RecalculateSingleRecord(DatasetRecord) then
                    RecalculatedCount += 1;
            until DatasetRecord.Next() = 0;

        Message(RecalculationCompletedMsg, RecalculatedCount);
    end;

    /// <summary>
    /// Fills gaps in the price dataset for all items that have existing records.
    /// Creates interpolated records for missing months between earliest and latest actual data.
    /// </summary>
    procedure FillAllPriceDatasetGaps()
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        ItemList: List of [Code[20]];
        ItemNo: Code[20];
    begin
        // Get all unique items that have actual records
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        DatasetRecord.SetFilter("Start Date", '<>%1', 0D); // Only actual records with dates

        if DatasetRecord.FindSet() then
            repeat
                if not ItemList.Contains(DatasetRecord."Item No.") then
                    ItemList.Add(DatasetRecord."Item No.");
            until DatasetRecord.Next() = 0;

        // Fill gaps for each item
        foreach ItemNo in ItemList do
            FillPriceDatasetGaps(ItemNo);

        Message(GapFillingCompletedForAllItemsMsg);
    end;

    /// <summary>
    /// Test method specifically for item MK000064 - Step 1: Just create monthly records.
    /// </summary>
    procedure TestProcessMK000064()
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        StartDate: Date;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
    begin
        // Get setup values
        IpekPamukSetup.GetRecordOnce();
        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Step 1: Create the monthly structure using working logic
        CreatedRecords := CreateCompleteMonthlyStructure('MK000064');

        // Step 2: Populate prices using existing proven logic
        PopulatedRecords := PopulatePricesFromInvoicesPublic('MK000064', StartDate);

        // Show comprehensive success message
        Message(TestCompletedForItemMsg,
            CreatedRecords, PopulatedRecords);
    end;

    /// <summary>
    /// Processes all items with Replenishment System = Prod. Order and shows processing time.
    /// </summary>
    procedure ProcessAllProductionItems()
    var
        Item: Record Item;
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        StartDate: Date;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMs: BigInteger;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
        ItemCount: Integer;
    begin
        // Get setup values
        IpekPamukSetup.GetRecordOnce();
        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // Process all items with Replenishment System = Prod. Order
        Item.SetRange("Replenishment System", Item."Replenishment System"::"Prod. Order");
        if Item.FindSet() then
            repeat
                ItemCount += 1;

                // Step 1: Create monthly structure for this item
                CreatedRecords := CreateCompleteMonthlyStructure(Item."No.");
                TotalCreated += CreatedRecords;

                // Step 2: Populate prices for this item
                PopulatedRecords := PopulatePricesFromInvoicesPublic(Item."No.", StartDate);
                TotalPopulated += PopulatedRecords;
            until Item.Next() = 0;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show comprehensive completion message with timing
        Message(SalesProcessingCompletedMsg,
            ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    /// <summary>
    /// Processes all items with Replenishment System = Purchase for purchase price data and shows processing time.
    /// </summary>
    procedure ProcessAllPurchaseItems()
    var
        Item: Record Item;
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        StartDate: Date;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMs: BigInteger;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
        ItemCount: Integer;
    begin
        // Get setup values
        IpekPamukSetup.GetRecordOnce();
        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // Process all items with Replenishment System = Purchase
        Item.SetRange("Replenishment System", Item."Replenishment System"::Purchase);
        if Item.FindSet() then
            repeat
                ItemCount += 1;

                // Step 1: Create monthly structure for this item
                CreatedRecords := CreateCompleteMonthlyStructureForPurchase(Item."No.");
                TotalCreated += CreatedRecords;

                // Step 2: Populate purchase prices for this item
                PopulatedRecords := PopulatePricesFromPurchaseEntries(Item."No.", StartDate);
                TotalPopulated += PopulatedRecords;
            until Item.Next() = 0;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show comprehensive completion message with timing
        Message(PurchaseProcessingCompletedMsg,
            ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    /// <summary>
    /// Intelligently processes Item Ledger Entries based on the item's Replenishment System.
    /// Uses purchase logic for Purchase items and sales logic for all other items.
    /// </summary>
    /// <param name="ItemNo">Item number to process</param>
    /// <param name="StartDate">Start date to process Item Ledger Entries from</param>
    /// <param name="ReplenishmentSystem">The item's replenishment system</param>
    procedure ProcessPriceDatasetFromItemIntelligent(ItemNo: Code[20]; StartDate: Date; ReplenishmentSystem: Enum "Replenishment System")
    var
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        ProcessingType: Text;
    begin
        // Validate input parameters
        if ItemNo = '' then
            Error(ItemNoMustBeSpecifiedErr);
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Process based on Replenishment System
        if ReplenishmentSystem = ReplenishmentSystem::Purchase then begin
            // Use purchase processing logic
            CreatedRecords := CreateCompleteMonthlyStructureForPurchase(ItemNo);
            PopulatedRecords := PopulatePricesFromPurchaseEntries(ItemNo, StartDate);
            ProcessingType := 'Purchase price dataset processing';
        end else begin
            // Use sales processing logic for all other replenishment systems
            CreatedRecords := CreateCompleteMonthlyStructure(ItemNo);
            PopulatedRecords := PopulatePricesFromInvoicesPublic(ItemNo, StartDate);
            ProcessingType := 'Sales price dataset processing';
        end;

        // Show completion message
        Message(IntelligentProcessingCompletedMsg,
            ProcessingType, ItemNo, CreatedRecords, PopulatedRecords);
    end;

    /// <summary>
    /// Backward compatibility wrapper for ProcessAllProductionItems.
    /// </summary>
    procedure ProcessAllMKItems()
    begin
        ProcessAllProductionItems();
    end;

    /// <summary>
    /// Backward compatibility wrapper for ProcessAllPurchaseItems.
    /// </summary>
    procedure ProcessAllIItemsPurchase()
    begin
        ProcessAllPurchaseItems();
    end;





    /// <summary>
    /// Creates monthly records using the working simple logic.
    /// </summary>
    /// <param name="ItemNo">Item number to create structure for</param>
    /// <param name="StartDate">Start date for monthly structure</param>
    /// <returns>Number of monthly records created</returns>
    local procedure CreateCompleteMonthlyStructure(ItemNo: Code[20]): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        LastRecord: Record "Item Price Dataset IPK";
        Item: Record Item;
        i: Integer;
        MonthText: Text;
        StartDate2: Date;
        EndDate: Date;
        NextEntryNo: Integer;
    begin
        // Get item description
        if not Item.Get(ItemNo) then
            Error(ItemNotFoundErr, ItemNo);

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Create records for current month count
        for i := 1 to WorkDate().Month() do begin
            DatasetRecord.Init();
            DatasetRecord."Entry No." := NextEntryNo;
            DatasetRecord."Item No." := ItemNo;
            DatasetRecord."Variant Code" := '';
            DatasetRecord."Description" := Item.Description;
            DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";

            // Format month as MM.YYYY
            if i < 10 then
                MonthText := '0' + Format(i) + '.2025'
            else
                MonthText := Format(i) + '.2025';

            DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
            DatasetRecord."Average Unit Price" := 0;

            // Calculate start and end dates for the month
            StartDate2 := DMY2Date(1, i, 2025); // First day of month
            EndDate := CalcDate('<CM>', StartDate2); // Last day of month

            DatasetRecord."Start Date" := StartDate2;
            DatasetRecord."End Date" := EndDate;
            DatasetRecord."Entry Type" := DatasetRecord."Entry Type"::Sale;

            DatasetRecord.Insert();
            NextEntryNo += 1;
        end;

        exit(WorkDate().Month());
    end;

    /// <summary>
    /// Creates monthly records for purchase data using the working simple logic.
    /// </summary>
    /// <param name="ItemNo">Item number to create structure for</param>
    /// <param name="StartDate">Start date for monthly structure</param>
    /// <returns>Number of monthly records created</returns>
    local procedure CreateCompleteMonthlyStructureForPurchase(ItemNo: Code[20]): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        LastRecord: Record "Item Price Dataset IPK";
        Item: Record Item;
        i: Integer;
        MonthText: Text;
        StartDate2: Date;
        EndDate: Date;
        NextEntryNo: Integer;
    begin
        // Get item description
        if not Item.Get(ItemNo) then
            Error(ItemNotFoundErr, ItemNo);

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Create records for current month count
        for i := 1 to WorkDate().Month() do begin
            DatasetRecord.Init();
            DatasetRecord."Entry No." := NextEntryNo;
            DatasetRecord."Item No." := ItemNo;
            DatasetRecord."Variant Code" := '';
            DatasetRecord."Description" := Item.Description;
            DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";

            // Format month as MM.YYYY
            if i < 10 then
                MonthText := '0' + Format(i) + '.2025'
            else
                MonthText := Format(i) + '.2025';

            DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
            DatasetRecord."Average Unit Price" := 0;

            // Calculate start and end dates for the month
            StartDate2 := DMY2Date(1, i, 2025); // First day of month
            EndDate := CalcDate('<CM>', StartDate2); // Last day of month

            DatasetRecord."Start Date" := StartDate2;
            DatasetRecord."End Date" := EndDate;
            DatasetRecord."Entry Type" := DatasetRecord."Entry Type"::Purchase;

            DatasetRecord.Insert();
            NextEntryNo += 1;
        end;

        exit(WorkDate().Month());
    end;

    /// <summary>
    /// Populates purchase prices from purchase invoice and purchase credit memo data and propagates prices for months without purchases.
    /// </summary>
    /// <param name="ItemNo">Item number to populate purchase prices for</param>
    /// <param name="StartDate">Start date for processing</param>
    /// <returns>Number of records populated with actual purchase invoice data</returns>
    procedure PopulatePricesFromPurchaseEntries(ItemNo: Code[20]; StartDate: Date): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        ValueEntry: Record "Value Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        PurchaseDataDict: Dictionary of [Text, Decimal];
        PurchaseQuantityDict: Dictionary of [Text, Decimal];
        PurchaseDataKey: Text;
        TotalCostAmount: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgCost: Decimal;
        PopulatedRecords: Integer;
        PreviousPrice: Decimal;
        CurrentMonth: Text;
        CostAmountACY: Decimal;
        ACYCurrencyCode: Code[10];
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // First pass: Collect actual purchase invoice data
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Purchase);
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetFilter("Quantity", '<>0');

        if ItemLedgerEntry.FindSet() then
            repeat
                // Get corresponding Value Entries for purchase invoice information
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Purchase);
                ValueEntry.SetFilter("Document Type", '%1|%2',
                    ValueEntry."Document Type"::"Purchase Invoice",
                    ValueEntry."Document Type"::"Purchase Credit Memo");
                ValueEntry.SetFilter("Cost Amount (Actual)", '<>0');
                ValueEntry.SetFilter("Invoiced Quantity", '<>0');

                if ValueEntry.FindSet() then
                    repeat
                        CurrentMonth := Format(ValueEntry."Posting Date", 0, '<Month,2>.<Year4>');
                        PurchaseDataKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Variant Code" + '|' + CurrentMonth;

                        // Accumulate cost amounts (ACY) and quantities
                        // Use ACY amount if available, otherwise fall back to LCY
                        CostAmountACY := ValueEntry."Cost Amount (Actual) (ACY)";
                        if CostAmountACY = 0 then
                            CostAmountACY := ValueEntry."Cost Amount (Actual)";

                        if PurchaseDataDict.ContainsKey(PurchaseDataKey) then
                            PurchaseDataDict.Set(PurchaseDataKey, PurchaseDataDict.Get(PurchaseDataKey) + CostAmountACY)
                        else
                            PurchaseDataDict.Add(PurchaseDataKey, CostAmountACY);

                        if PurchaseQuantityDict.ContainsKey(PurchaseDataKey) then
                            PurchaseQuantityDict.Set(PurchaseDataKey, PurchaseQuantityDict.Get(PurchaseDataKey) + ValueEntry."Invoiced Quantity")
                        else
                            PurchaseQuantityDict.Add(PurchaseDataKey, ValueEntry."Invoiced Quantity");
                    until ValueEntry.Next() = 0;
            until ItemLedgerEntry.Next() = 0;

        // Second pass: Update records with calculated purchase prices and propagate
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Purchase);
        DatasetRecord.SetCurrentKey("Item No.", "Variant Code", "Source Price Month");

        if DatasetRecord.FindSet() then
            repeat
                PurchaseDataKey := DatasetRecord."Item No." + '|' + DatasetRecord."Variant Code" + '|' + DatasetRecord."Source Price Month";

                if PurchaseDataDict.ContainsKey(PurchaseDataKey) then begin
                    // Calculate weighted average cost from actual purchase data
                    TotalCostAmount := PurchaseDataDict.Get(PurchaseDataKey);
                    TotalQuantity := PurchaseQuantityDict.Get(PurchaseDataKey);

                    if TotalQuantity <> 0 then begin
                        WeightedAvgCost := TotalCostAmount / TotalQuantity;
                        DatasetRecord."Average Unit Price" := WeightedAvgCost;
                        DatasetRecord."Currency Code" := ACYCurrencyCode;
                        DatasetRecord.Modify(true);
                        PopulatedRecords += 1;
                        PreviousPrice := WeightedAvgCost; // Store for propagation
                    end;
                end else begin
                    // No purchase data for this month - use previous price if available
                    if (PreviousPrice <> 0) and (DatasetRecord."Average Unit Price" = 0) then begin
                        DatasetRecord."Average Unit Price" := PreviousPrice;
                        DatasetRecord."Currency Code" := ACYCurrencyCode;
                        DatasetRecord.Modify(true);
                    end;
                end;
            until DatasetRecord.Next() = 0;

        exit(PopulatedRecords);
    end;

    /// <summary>
    /// Populates prices from sales invoice and sales credit memo data and propagates prices for months without sales.
    /// </summary>
    /// <param name="ItemNo">Item number to populate prices for</param>
    /// <param name="StartDate">Start date for processing</param>
    /// <returns>Number of records populated with actual invoice data</returns>
    procedure PopulatePricesFromInvoicesPublic(ItemNo: Code[20]; StartDate: Date): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        ValueEntry: Record "Value Entry";
        ItemLedgerEntry: Record "Item Ledger Entry";
        CurrencyExchangeRate: Record "Currency Exchange Rate";
        InvoiceDataDict: Dictionary of [Text, Decimal];
        InvoiceQuantityDict: Dictionary of [Text, Decimal];
        SalesDataKey: Text;
        TotalSalesAmount: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgPrice: Decimal;
        PopulatedRecords: Integer;
        PreviousPrice: Decimal;
        CurrentMonth: Text;
        SalesAmountACY: Decimal;
        ACYCurrencyCode: Code[10];
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // First pass: Collect actual invoice data
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Sale);
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetFilter("Quantity", '<>0');

        if ItemLedgerEntry.FindSet() then
            repeat
                // Get corresponding Value Entries for invoice information
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
                ValueEntry.SetFilter("Document Type", '%1|%2',
                    ValueEntry."Document Type"::"Sales Invoice",
                    ValueEntry."Document Type"::"Sales Credit Memo");
                ValueEntry.SetFilter("Sales Amount (Actual)", '<>0');
                ValueEntry.SetFilter("Invoiced Quantity", '<>0');

                if ValueEntry.FindSet() then
                    repeat
                        CurrentMonth := Format(ValueEntry."Posting Date", 0, '<Month,2>.<Year4>');
                        SalesDataKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Variant Code" + '|' + CurrentMonth;

                        // Convert sales amount from LCY to ACY
                        if ACYCurrencyCode <> '' then begin
                            SalesAmountACY := CurrencyExchangeRate.ExchangeAmtLCYToFCY(
                                ValueEntry."Posting Date",
                                ACYCurrencyCode,
                                ValueEntry."Sales Amount (Actual)",
                                CurrencyExchangeRate.ExchangeRate(ValueEntry."Posting Date", ACYCurrencyCode));
                        end else
                            SalesAmountACY := ValueEntry."Sales Amount (Actual)";

                        // Accumulate sales amounts (ACY) and quantities
                        if InvoiceDataDict.ContainsKey(SalesDataKey) then
                            InvoiceDataDict.Set(SalesDataKey, InvoiceDataDict.Get(SalesDataKey) + SalesAmountACY)
                        else
                            InvoiceDataDict.Add(SalesDataKey, SalesAmountACY);

                        if InvoiceQuantityDict.ContainsKey(SalesDataKey) then
                            InvoiceQuantityDict.Set(SalesDataKey, InvoiceQuantityDict.Get(SalesDataKey) + ValueEntry."Invoiced Quantity")
                        else
                            InvoiceQuantityDict.Add(SalesDataKey, ValueEntry."Invoiced Quantity");
                    until ValueEntry.Next() = 0;
            until ItemLedgerEntry.Next() = 0;

        // Second pass: Update records with calculated prices and propagate
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        DatasetRecord.SetCurrentKey("Item No.", "Variant Code", "Source Price Month");

        if DatasetRecord.FindSet() then
            repeat
                SalesDataKey := DatasetRecord."Item No." + '|' + DatasetRecord."Variant Code" + '|' + DatasetRecord."Source Price Month";

                if InvoiceDataDict.ContainsKey(SalesDataKey) then begin
                    // Calculate weighted average price from actual data
                    TotalSalesAmount := InvoiceDataDict.Get(SalesDataKey);
                    TotalQuantity := InvoiceQuantityDict.Get(SalesDataKey);

                    if TotalQuantity <> 0 then begin
                        WeightedAvgPrice := TotalSalesAmount / TotalQuantity;
                        DatasetRecord."Average Unit Price" := WeightedAvgPrice;
                        DatasetRecord."Currency Code" := ACYCurrencyCode;
                        DatasetRecord.Modify(true);
                        PopulatedRecords += 1;
                        PreviousPrice := WeightedAvgPrice; // Store for propagation
                    end;
                end else begin
                    // No invoice data for this month - use previous price if available
                    if (PreviousPrice <> 0) and (DatasetRecord."Average Unit Price" = 0) then begin
                        DatasetRecord."Average Unit Price" := PreviousPrice;
                        DatasetRecord."Currency Code" := ACYCurrencyCode;
                        DatasetRecord.Modify(true);
                    end;
                end;
            until DatasetRecord.Next() = 0;

        exit(PopulatedRecords);
    end;

    local procedure RecalculateSingleRecord(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        ValueEntry: Record "Value Entry";
        TotalSalesAmount: Decimal;
        TotalQuantity: Decimal;
        NewAvgPrice: Decimal;
    begin
        // Recalculate based on current Value Entry data using invoice dates
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");
        ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");
        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");
        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
        ValueEntry.SetFilter("Document Type", '%1|%2',
            ValueEntry."Document Type"::"Sales Invoice",
            ValueEntry."Document Type"::"Sales Credit Memo");
        ValueEntry.SetFilter("Sales Amount (Actual)", '<>0');
        ValueEntry.SetFilter("Invoiced Quantity", '<>0');

        if ValueEntry.FindSet() then begin
            repeat
                TotalSalesAmount += ValueEntry."Sales Amount (Actual)";
                TotalQuantity += ValueEntry."Invoiced Quantity";
            until ValueEntry.Next() = 0;

            if TotalQuantity <> 0 then begin
                NewAvgPrice := TotalSalesAmount / TotalQuantity;
                if DatasetRecord."Average Unit Price" <> NewAvgPrice then begin
                    DatasetRecord."Average Unit Price" := NewAvgPrice;
                    DatasetRecord.Modify(true);
                    exit(true);
                end;
            end;
        end;

        exit(false);
    end;

    /// <summary>
    /// Processes Item Ledger Entries from the specified start date forward and creates/updates monthly price dataset records based on invoice dates.
    /// New approach: Creates complete monthly structure first, then populates prices from invoice and credit memo data.
    /// </summary>
    /// <param name="ItemNo">Item number to process</param>
    /// <param name="StartDate">Start date to process Item Ledger Entries from</param>
    procedure ProcessPriceDatasetFromItem(ItemNo: Code[20]; StartDate: Date)
    var
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
    begin
        // Validate input parameters
        if ItemNo = '' then
            Error(ItemNoMustBeSpecifiedErr);
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Step 1: Create complete monthly structure using working logic
        CreatedRecords := CreateCompleteMonthlyStructure(ItemNo);

        // Step 2: Populate prices using existing proven logic
        PopulatedRecords := PopulatePricesFromInvoicesPublic(ItemNo, StartDate);

        // Show completion message
        Message(PriceDatasetProcessingCompletedForItemMsg,
            ItemNo, CreatedRecords, PopulatedRecords);
    end;

    local procedure ProcessMonthlyDataWithUpsert(ItemNo: Code[20]; MonthStartDate: Date; MonthEndDate: Date; var ProcessedRecords: Integer)
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        ValueEntry: Record "Value Entry";
        ExistingRecord: Record "Item Price Dataset IPK";
        InvoiceDataDict: Dictionary of [Text, Decimal];
        InvoiceQuantityDict: Dictionary of [Text, Decimal];
        InvoiceMonthDict: Dictionary of [Text, Text];
        SalesDataKey: Text;
        KeyParts: List of [Text];
        TotalSalesAmount: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgPrice: Decimal;
        ProcessingItemNo: Code[20];
        VariantCode: Code[10];
        SourcePriceMonth: Text[7];
        InvoiceDate: Date;
        InvoiceMonthKey: Text;
    begin
        // Process Item Ledger Entries for sales transactions for the specified item
        // Note: We now process all Item Ledger Entries and group by invoice date instead of shipment date
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Sale);
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetFilter("Quantity", '<>0');

        if not ItemLedgerEntry.FindSet() then
            exit; // No data for this item

        // Accumulate data by item, variant, and invoice month
        repeat
            // Get corresponding Value Entries for invoice information
            ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
            ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
            ValueEntry.SetFilter("Document Type", '%1|%2',
                ValueEntry."Document Type"::"Sales Invoice",
                ValueEntry."Document Type"::"Sales Credit Memo");
            ValueEntry.SetFilter("Sales Amount (Actual)", '<>0');
            ValueEntry.SetFilter("Invoiced Quantity", '<>0');

            if ValueEntry.FindSet() then
                repeat
                    // Use invoice posting date instead of shipment posting date
                    InvoiceDate := ValueEntry."Posting Date";

                    // Only process invoices within the specified month range
                    if (InvoiceDate >= MonthStartDate) and (InvoiceDate <= MonthEndDate) then begin
                        // Create unique key for grouping by item, variant, and invoice month
                        InvoiceMonthKey := Format(InvoiceDate, 0, '<Month,2>.<Year4>');
                        SalesDataKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Variant Code" + '|' + InvoiceMonthKey;

                        // Store the invoice month for this key
                        if not InvoiceMonthDict.ContainsKey(SalesDataKey) then
                            InvoiceMonthDict.Add(SalesDataKey, InvoiceMonthKey);

                        // Accumulate sales amounts from Value Entries
                        if InvoiceDataDict.ContainsKey(SalesDataKey) then
                            InvoiceDataDict.Set(SalesDataKey, InvoiceDataDict.Get(SalesDataKey) + ValueEntry."Sales Amount (Actual)")
                        else
                            InvoiceDataDict.Add(SalesDataKey, ValueEntry."Sales Amount (Actual)");

                        // Accumulate quantities from Value Entries
                        if InvoiceQuantityDict.ContainsKey(SalesDataKey) then
                            InvoiceQuantityDict.Set(SalesDataKey, InvoiceQuantityDict.Get(SalesDataKey) + ValueEntry."Invoiced Quantity")
                        else
                            InvoiceQuantityDict.Add(SalesDataKey, ValueEntry."Invoiced Quantity");
                    end;
                until ValueEntry.Next() = 0;

        until ItemLedgerEntry.Next() = 0;

        // Create or update records from accumulated invoice data
        foreach SalesDataKey in InvoiceDataDict.Keys() do begin
            // Parse the key to extract Item No., Variant Code, and Invoice Month
            KeyParts := SalesDataKey.Split('|');
            if KeyParts.Count() >= 3 then begin
                ProcessingItemNo := CopyStr(KeyParts.Get(1), 1, 20);
                VariantCode := CopyStr(KeyParts.Get(2), 1, 10);
                SourcePriceMonth := CopyStr(KeyParts.Get(3), 1, 7);
            end else
                continue; // Skip invalid keys

            // Get totals from dictionaries
            TotalSalesAmount := InvoiceDataDict.Get(SalesDataKey);
            TotalQuantity := InvoiceQuantityDict.Get(SalesDataKey);

            // Calculate weighted average price
            if TotalQuantity <> 0 then
                WeightedAvgPrice := TotalSalesAmount / TotalQuantity
            else
                WeightedAvgPrice := 0;

            // Calculate month start and end dates from the invoice month
            InvoiceDate := WorkDate(); // Default to current date
            if SourcePriceMonth <> '' then begin
                // Parse MM.YYYY format to get actual month/year
                if StrLen(SourcePriceMonth) = 7 then begin
                    Evaluate(InvoiceDate, '01.' + CopyStr(SourcePriceMonth, 1, 2) + '.' + CopyStr(SourcePriceMonth, 4, 4));
                end;
            end;
            MonthStartDate := CalcDate('<-CM>', InvoiceDate); // First day of invoice month
            MonthEndDate := CalcDate('<CM>', InvoiceDate); // Last day of invoice month

            // Price calculation completed for this invoice month

            // Check if record already exists for this item, variant, and invoice month
            ExistingRecord.SetRange("Item No.", ProcessingItemNo);
            ExistingRecord.SetRange("Variant Code", VariantCode);
            ExistingRecord.SetRange("Source Price Month", SourcePriceMonth);
            ExistingRecord.SetRange("Entry Type", ExistingRecord."Entry Type"::Sale);

            if ExistingRecord.FindFirst() then begin
                // Update existing record
                ExistingRecord."Average Unit Price" := WeightedAvgPrice;
                ExistingRecord."Start Date" := MonthStartDate;
                ExistingRecord."End Date" := MonthEndDate;
                ExistingRecord.Modify(true);
                ProcessedRecords += 1;
            end else begin
                // Insert new record
                ExistingRecord.Init();
                ExistingRecord."Item No." := ProcessingItemNo;
                ExistingRecord.Validate("Item No."); // This will populate Description and Base Unit of Measure Code
                ExistingRecord."Variant Code" := VariantCode;
                if ExistingRecord."Variant Code" <> '' then
                    ExistingRecord.Validate("Variant Code"); // This will update Description if variant exists
                ExistingRecord."Source Price Month" := SourcePriceMonth;
                ExistingRecord."Average Unit Price" := WeightedAvgPrice;
                ExistingRecord."Start Date" := MonthStartDate;
                ExistingRecord."End Date" := MonthEndDate;
                ExistingRecord."Entry Type" := ExistingRecord."Entry Type"::Sale;

                if ExistingRecord.Insert(true) then
                    ProcessedRecords += 1;
            end;
        end;
    end;

    /// <summary>
    /// Fills gaps between actual invoice months with interpolated price data for a specific item.
    /// Creates records for missing months using the price from the nearest previous month.
    /// Also extends price data from the latest actual month to the current month.
    /// </summary>
    /// <param name="ItemNo">Item number to process gap filling for</param>
    local procedure FillPriceDatasetGaps(ItemNo: Code[20])
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        ItemVariantList: List of [Text];
        VariantKey: Text;
        InterpolatedRecords: Integer;
    begin
        // Get all unique item/variant combinations for the specified item
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        DatasetRecord.SetFilter("Start Date", '<>%1', 0D); // Only actual records with dates

        if DatasetRecord.FindSet() then
            repeat
                VariantKey := DatasetRecord."Item No." + '|' + DatasetRecord."Variant Code";
                if not ItemVariantList.Contains(VariantKey) then
                    ItemVariantList.Add(VariantKey);
            until DatasetRecord.Next() = 0;

        // Process gap filling for each item/variant combination
        foreach VariantKey in ItemVariantList do
            InterpolatedRecords += FillGapsForItemVariant(VariantKey);

        // Gap filling completed silently - message will be shown by calling method
    end;

    /// <summary>
    /// Fills gaps for a specific item/variant combination.
    /// </summary>
    /// <param name="ItemVariantKey">Key in format "ItemNo|VariantCode"</param>
    /// <returns>Number of interpolated records created</returns>
    local procedure FillGapsForItemVariant(ItemVariantKey: Text): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        KeyParts: List of [Text];
        ItemNo: Code[20];
        VariantCode: Code[10];
        ActualMonths: List of [Text];
        EarliestMonth: Text;
        LatestMonth: Text;
        InterpolatedCount: Integer;
    begin
        // Parse the key to extract Item No. and Variant Code
        KeyParts := ItemVariantKey.Split('|');
        if KeyParts.Count() < 2 then
            exit(0);

        ItemNo := CopyStr(KeyParts.Get(1), 1, 20);
        VariantCode := CopyStr(KeyParts.Get(2), 1, 10);

        // Get all actual months for this item/variant combination (sorted)
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Variant Code", VariantCode);
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        DatasetRecord.SetFilter("Start Date", '<>%1', 0D); // Only actual records with dates
        DatasetRecord.SetCurrentKey("Source Price Month");

        if not DatasetRecord.FindSet() then
            exit(0); // No actual records found

        // Collect all actual months and find earliest/latest
        repeat
            if not ActualMonths.Contains(DatasetRecord."Source Price Month") then
                ActualMonths.Add(DatasetRecord."Source Price Month");
        until DatasetRecord.Next() = 0;

        if ActualMonths.Count() < 1 then
            exit(0); // Need at least 1 month to extend to current month

        // Sort the months (AL List is automatically sorted for text)
        EarliestMonth := ActualMonths.Get(1);
        LatestMonth := ActualMonths.Get(ActualMonths.Count());

        // Fill gaps between earliest and latest months (only if we have multiple months)
        if ActualMonths.Count() >= 2 then
            InterpolatedCount := FillGapsBetweenMonths(ItemNo, VariantCode, EarliestMonth, LatestMonth, ActualMonths);

        // Always fill gaps from latest month to current month
        InterpolatedCount += FillGapsToCurrentMonth(ItemNo, VariantCode, LatestMonth, ActualMonths);

        exit(InterpolatedCount);
    end;

    /// <summary>
    /// Fills gaps between two months for a specific item/variant.
    /// </summary>
    local procedure FillGapsBetweenMonths(ItemNo: Code[20]; VariantCode: Code[10]; EarliestMonth: Text; LatestMonth: Text; ActualMonths: List of [Text]): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        ItemPriceDataset: Record "Item Price Dataset IPK";
        NewRecord: Record "Item Price Dataset IPK";
        CurrentDate: Date;
        EndDate: Date;
        CurrentMonth: Text;
        PreviousPrice: Decimal;
        InterpolatedCount: Integer;
    begin
        // Parse earliest month to get starting date
        if not ParseMonthToDate(EarliestMonth, CurrentDate) then
            exit(0);

        // Parse latest month to get ending date
        if not ParseMonthToDate(LatestMonth, EndDate) then
            exit(0);

        // Get template record for item/variant information
        ItemPriceDataset.SetRange("Item No.", ItemNo);
        ItemPriceDataset.SetRange("Variant Code", VariantCode);
        ItemPriceDataset.SetRange("Entry Type", ItemPriceDataset."Entry Type"::Sale);
        if not ItemPriceDataset.FindFirst() then
            exit(0);

        // Initialize previous price with the earliest month's price
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Variant Code", VariantCode);
        DatasetRecord.SetRange("Source Price Month", EarliestMonth);
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        if DatasetRecord.FindFirst() then
            PreviousPrice := DatasetRecord."Average Unit Price"
        else
            exit(0);

        // Iterate through each month from earliest to latest
        while CurrentDate <= EndDate do begin
            CurrentMonth := Format(CurrentDate, 0, '<Month,2>.<Year4>');

            // Check if this month already has actual data
            if not ActualMonths.Contains(CurrentMonth) then begin
                // Create interpolated record for missing month
                NewRecord.Init();
                NewRecord."Item No." := ItemNo;
                NewRecord.Validate("Item No."); // This will populate Description and Base Unit of Measure Code
                NewRecord."Variant Code" := VariantCode;
                if NewRecord."Variant Code" <> '' then
                    NewRecord.Validate("Variant Code"); // This will update Description if variant exists
                NewRecord."Source Price Month" := CurrentMonth;
                NewRecord."Average Unit Price" := PreviousPrice;
                // Set proper start and end dates for interpolated records
                NewRecord."Start Date" := CalcDate('<-CM>', CurrentDate); // First day of month
                NewRecord."End Date" := CalcDate('<CM>', CurrentDate); // Last day of month
                NewRecord."Entry Type" := NewRecord."Entry Type"::Sale;

                if NewRecord.Insert(true) then
                    InterpolatedCount += 1;
            end else begin
                // Update previous price with actual data from this month
                DatasetRecord.SetRange("Source Price Month", CurrentMonth);
                if DatasetRecord.FindFirst() then
                    PreviousPrice := DatasetRecord."Average Unit Price";
            end;

            // Move to next month
            CurrentDate := CalcDate('<1M>', CurrentDate);
        end;

        exit(InterpolatedCount);
    end;

    /// <summary>
    /// Parses a month string in MM.YYYY format to a date.
    /// </summary>
    /// <param name="MonthString">Month in MM.YYYY format</param>
    /// <param name="ResultDate">Resulting date (first day of the month)</param>
    /// <returns>True if parsing was successful</returns>
    local procedure ParseMonthToDate(MonthString: Text; var ResultDate: Date): Boolean
    var
        Month: Integer;
        Year: Integer;
    begin
        if StrLen(MonthString) <> 7 then
            exit(false);

        if not Evaluate(Month, CopyStr(MonthString, 1, 2)) then
            exit(false);

        if not Evaluate(Year, CopyStr(MonthString, 4, 4)) then
            exit(false);

        if (Month < 1) or (Month > 12) then
            exit(false);

        if (Year < 1900) or (Year > 2100) then
            exit(false);

        ResultDate := DMY2Date(1, Month, Year);
        exit(true);
    end;

    /// <summary>
    /// Fills gaps from the latest actual month to the current month.
    /// </summary>
    /// <param name="ItemNo">Item number</param>
    /// <param name="VariantCode">Variant code</param>
    /// <param name="LatestMonth">Latest month with actual data</param>
    /// <param name="ActualMonths">List of months with actual data</param>
    /// <returns>Number of interpolated records created</returns>
    local procedure FillGapsToCurrentMonth(ItemNo: Code[20]; VariantCode: Code[10]; LatestMonth: Text; ActualMonths: List of [Text]): Integer
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        NewRecord: Record "Item Price Dataset IPK";
        LatestDate: Date;
        CurrentDate: Date;
        EndDate: Date;
        CurrentMonth: Text;
        LatestPrice: Decimal;
        InterpolatedCount: Integer;
    begin
        // Fill gaps from latest actual month to current month
        // Parse latest month to get starting date
        if not ParseMonthToDate(LatestMonth, LatestDate) then
            exit(0);

        // Get the price from the latest actual month
        DatasetRecord.SetRange("Item No.", ItemNo);
        DatasetRecord.SetRange("Variant Code", VariantCode);
        DatasetRecord.SetRange("Source Price Month", LatestMonth);
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        if not DatasetRecord.FindFirst() then
            exit(0);

        LatestPrice := DatasetRecord."Average Unit Price";

        // Set end date to ensure we include current month and go beyond
        // Calculate the end of current month and add one more month
        EndDate := CalcDate('<CM+1M>', WorkDate()); // End of current month + 1 month

        // Start from the month after the latest actual month
        CurrentDate := CalcDate('<1M>', LatestDate);

        // Ensure we always process at least up to the current month
        if EndDate < CalcDate('<-CM>', WorkDate()) then
            EndDate := CalcDate('<CM>', WorkDate()); // At least to end of current month

        // Fill gaps from latest month to current month (and beyond)
        while CurrentDate <= EndDate do begin
            CurrentMonth := Format(CurrentDate, 0, '<Month,2>.<Year4>');

            // Check if this month already has actual data
            if not ActualMonths.Contains(CurrentMonth) then begin
                // Check if any record (actual or interpolated) already exists
                DatasetRecord.SetRange("Source Price Month", CurrentMonth);
                if DatasetRecord.FindFirst() then begin
                    // Update existing interpolated record if it has blank dates
                    if (DatasetRecord."Start Date" = 0D) or (DatasetRecord."End Date" = 0D) then begin
                        DatasetRecord."Start Date" := CalcDate('<-CM>', CurrentDate);
                        DatasetRecord."End Date" := CalcDate('<CM>', CurrentDate);
                        DatasetRecord."Average Unit Price" := LatestPrice;
                        DatasetRecord.Modify(true);
                        InterpolatedCount += 1;
                    end;
                end else begin
                    // Create new interpolated record for missing month
                    NewRecord.Init();
                    NewRecord."Item No." := ItemNo;
                    NewRecord.Validate("Item No."); // This will populate Description and Base Unit of Measure Code
                    NewRecord."Variant Code" := VariantCode;
                    if NewRecord."Variant Code" <> '' then
                        NewRecord.Validate("Variant Code"); // This will update Description if variant exists
                    NewRecord."Source Price Month" := CurrentMonth;
                    NewRecord."Average Unit Price" := LatestPrice;
                    // Set proper start and end dates for interpolated records
                    NewRecord."Start Date" := CalcDate('<-CM>', CurrentDate); // First day of month
                    NewRecord."End Date" := CalcDate('<CM>', CurrentDate); // Last day of month
                    NewRecord."Entry Type" := NewRecord."Entry Type"::Sale;

                    if NewRecord.Insert(true) then
                        InterpolatedCount += 1;
                end;
            end;

            // Move to next month
            CurrentDate := CalcDate('<1M>', CurrentDate);
        end;

        // Final check: Ensure current month is always included
        CurrentMonth := Format(WorkDate(), 0, '<Month,2>.<Year4>');
        if not ActualMonths.Contains(CurrentMonth) then begin
            DatasetRecord.SetRange("Source Price Month", CurrentMonth);
            if not DatasetRecord.FindFirst() then begin
                // Create record for current month if it doesn't exist
                NewRecord.Init();
                NewRecord."Item No." := ItemNo;
                NewRecord.Validate("Item No.");
                NewRecord."Variant Code" := VariantCode;
                if NewRecord."Variant Code" <> '' then
                    NewRecord.Validate("Variant Code");
                NewRecord."Source Price Month" := CurrentMonth;
                NewRecord."Average Unit Price" := LatestPrice;
                NewRecord."Start Date" := CalcDate('<-CM>', WorkDate());
                NewRecord."End Date" := CalcDate('<CM>', WorkDate());
                NewRecord."Entry Type" := NewRecord."Entry Type"::Sale;

                if NewRecord.Insert(true) then
                    InterpolatedCount += 1;
            end;
        end;

        exit(InterpolatedCount);
    end;

    /// <summary>
    /// Fills gaps for all items that have records in the specified date range.
    /// </summary>
    /// <param name="StartDate">Start date of the range</param>
    /// <param name="EndDate">End date of the range</param>
    local procedure FillPriceDatasetGapsForDateRange(StartDate: Date; EndDate: Date)
    var
        DatasetRecord: Record "Item Price Dataset IPK";
        ItemList: List of [Code[20]];
        ItemNo: Code[20];
    begin
        // Get all unique items that have records in the date range
        DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        DatasetRecord.SetFilter("Start Date", '<>%1', 0D); // Only actual records with dates
        DatasetRecord.SetFilter("Start Date", '>=%1&<=%2', StartDate, EndDate);

        if DatasetRecord.FindSet() then
            repeat
                if not ItemList.Contains(DatasetRecord."Item No.") then
                    ItemList.Add(DatasetRecord."Item No.");
            until DatasetRecord.Next() = 0;

        // Fill gaps for each item
        foreach ItemNo in ItemList do
            FillPriceDatasetGaps(ItemNo);
    end;

    /// <summary>
    /// Gets the Additional Reporting Currency code from General Ledger Setup.
    /// </summary>
    /// <returns>ACY currency code, or empty string if not configured</returns>
    local procedure GetACYCurrencyCode(): Code[10]
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
    begin
        if GeneralLedgerSetup.Get() then
            exit(GeneralLedgerSetup."Additional Reporting Currency");
        exit('');
    end;
}
