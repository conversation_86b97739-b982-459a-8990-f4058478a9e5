table 60015 "Package Transfer Line IPK"
{
    Caption = 'Package Transfer Line';
    DataClassification = CustomerContent;
    LookupPageId = "Package Transfer Details IPK";
    DrillDownPageId = "Package Transfer Details IPK";
    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            TableRelation = "Package No. Information"."Package No.";
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(4; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(5; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(6; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(7; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
            //     FieldClass = FlowField;
            //     CalcFormula = lookup("Package No. Information"."Total Quantity IPK" where("Package No." = field("Package No.")));

        }
        field(8; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(9; "Quantity To Transfer"; Decimal)
        {
            Caption = 'Quantity To Transfer';
            ToolTip = 'Specifies the value of the Quantity To Transfer field.';
            MinValue = 0;
            trigger OnValidate()


            begin
                if Rec."Quantity To Transfer" > Rec.Quantity then
                    Error(MoreThenErr, Rec.Quantity);

                if Rec."Quantity To Transfer" <> xRec."Quantity To Transfer" then begin

                    PackageTransferHeader.Get(Rec."Document No.");
                    PackageTransferHeader.Validate(Shipped, false);
                    PackageTransferHeader.Modify(true);
                end;

            end;
        }
        field(10; "Transfer-from Code"; Code[10])
        {
            Caption = 'Transfer-from Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = lookup("Package Transfer Header IPK"."Transfer-from Code" where("No." = field("Document No.")));

        }
        field(12; "Total Qty. on Location"; Decimal)
        {
            Caption = 'Total Qty. on Location';
            Editable = false;
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = sum("Item Ledger Entry"."Quantity" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Location Code" = field("Transfer-from Code"), "Lot No." = field("Lot No.")/*, Open = const(true)*/));
            ToolTip = 'Specifies the value of the Total Qty. on Location field.';
        }
        field(17; "Total Qty. on Loc. Trans"; Decimal)
        {
            Caption = 'Total Qty. on Loc. Trans';
            Editable = false;
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = sum("Package Transfer Line IPK"."Quantity To Transfer" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Lot No." = field("Lot No."), "Document No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Total Qty. on Location field.';
        }
        field(18; "Transfer-to Code"; Code[10])
        {
            Caption = 'Transfer-to Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Transfer-to Code field.';
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = lookup("Package Transfer Header IPK"."Transfer-to Code" where("No." = field("Document No.")));

        }
        field(19; Received; Boolean)
        {
            Caption = 'Received';
            Editable = false;
            ToolTip = 'Specifies the value of the Received field.';
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = lookup("Package Transfer Header IPK".Received where("No." = field("Document No.")));

        }
        field(20; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Posting Date field.';
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = lookup("Package Transfer Header IPK"."Posting Date" where("No." = field("Document No.")));

        }
        field(21; "Package Creation Date"; DateTime)
        {
            Caption = 'Package Creation Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Package Creation Date field.';
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = lookup("Package No. Information"."Created At IPK" where("Package No." = field("Package No.")));

        }
        field(22; "Current Package Location"; Code[10])
        {
            Caption = 'Current Package Location';
            Editable = false;
            ToolTip = 'Specifies the value of the Current Package Location field.';
            FieldClass = FlowField;//Burayı denize soracağım
            CalcFormula = lookup("Package No. Information"."Location Code IPK" where("Package No." = field("Package No.")));

        }
        // field(23; "Quantity At Creation"; Decimal)
        // {
        //     Caption = 'Quantity At Creation';
        //     ToolTip = 'Specifies the value of the Quantity At Creation field.';
        //     //     FieldClass = FlowField;
        //     //     CalcFormula = lookup("Package No. Information"."Total Quantity IPK" where("Package No." = field("Package No.")));

        // }
        // field(23; "Live Quantity"; Decimal)
        // {
        //     Caption = 'Live Quantity';
        //     ToolTip = 'Specifies the value of the Live Quantity field.';
        //     AllowInCustomizations = Always;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Package No. Information"."Total Quantity IPK" where("Package No." = field("Package No.")));

        // }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        // key(key2; "Document No.", "Package No.")
        // {
        //     Unique = true;
        // }
        // key(SK; "Document No.", "Package No.")
        // {
        //     Unique = true;
        // }
    }
    trigger OnInsert()
    var
        PackageTransferLine: Record "Package Transfer Line IPK";
    begin
        PackageTransferLine.SetRange("Document No.", Rec."Document No.");
        if PackageTransferLine.FindLast() then
            Rec."Line No." := PackageTransferLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    var
        PackageTransferHeader: Record "Package Transfer Header IPK";
        MoreThenErr: Label 'You cannot transfer more then %1', Comment = '%1="Package Transfer Line IPK".Quantity';
}