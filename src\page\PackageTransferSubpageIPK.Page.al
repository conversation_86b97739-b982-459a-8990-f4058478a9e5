page 60019 "Package Transfer Subpage IPK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Subpage IPK';
    PageType = ListPart;
    SourceTable = "Package Transfer Line IPK";
    InsertAllowed = false;
    SourceTableView = sorting("Document No.", "Line No.") order(descending);

    // Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    Editable = false;
                    QuickEntry = false;
                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.SetRange("Package No.", Rec."Package No.");
                        PackageNoInformation.FindFirst();
                        PageManagement.PageRun(PackageNoInformation);
                    end;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field(Description; Rec.Description)
                {
                    Editable = false;
                    QuickEntry = false;
                }
                // field(Quantity; LiveQuantity)
                // {
                //     Editable = false;
                //     QuickEntry = false;
                // }
                field("Current Quantity"; LiveQuantity)
                {
                    Caption = 'Current Quantity';
                    ToolTip = 'Specifies the value of the Current Quantity field.';
                    Editable = false;
                    QuickEntry = false;
                }
                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                    QuickEntry = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Total Qty. on Location"; Rec."Total Qty. on Location")
                {
                }
                field("Total Qty. on Loc. Trans"; Rec."Total Qty. on Loc. Trans")
                {
                    ToolTip = 'Specifies the value of the Total Qty. on Loc. Pack" field.';
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }
                // field("Transfer-from Code"; Rec."Transfer-from Code")
                // {
                //     Editable = false;
                //     QuickEntry = false;
                // }
                // field("Transfer-from Bin Code"; Rec."Transfer-from Bin Code")
                // {
                //     Editable = false;
                //     QuickEntry = false;
                // }
                // field("Transfer-to Code"; Rec."Transfer-to Code")
                // {
                //     QuickEntry = false;
                // }
                // field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                // {
                //     QuickEntry = false;
                // }
            }
        }
    }
    trigger OnNewRecord(BelowxRec: Boolean)
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
    begin
        PackageNoInfoLine.SetRange("Package No.", Rec."Package No.");
        PackageNoInfoLine.SetRange("Lot No.", Rec."Lot No.");
        PackageNoInfoLine.SetRange("Line Item No.", Rec."Item No.");
        PackageNoInfoLine.SetRange("Line Variant Code", Rec."Variant Code");
        if PackageNoInfoLine.FindFirst() then
            Rec.Quantity := PackageNoInfoLine.Quantity;
    end;

    trigger OnAfterGetRecord()
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
    begin
        PackageNoInfoLine.SetRange("Package No.", Rec."Package No.");
        PackageNoInfoLine.SetRange("Lot No.", Rec."Lot No.");
        PackageNoInfoLine.SetRange("Line Item No.", Rec."Item No.");
        PackageNoInfoLine.SetRange("Line Variant Code", Rec."Variant Code");
        if PackageNoInfoLine.FindFirst() then
            LiveQuantity := PackageNoInfoLine.Quantity;
    end;

    var
        PageManagement: Codeunit "Page Management";
        LiveQuantity: Decimal;
}