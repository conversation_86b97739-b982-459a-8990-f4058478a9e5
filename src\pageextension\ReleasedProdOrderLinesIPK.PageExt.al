pageextension 60015 "Released Prod. Order Lines IPK" extends "Released Prod. Order Lines"
{
    layout
    {
        modify("Location Code")
        {
            Visible = true;
        }
        modify("Variant Code")
        {
            Visible = true;
        }

    }
    actions
    {
        addlast("&Line")
        {
            action("CreatePackage IPK")
            {
                ApplicationArea = All;
                Caption = 'Create Package';
                Image = Action;
                ToolTip = 'Executes the Create Package action.';

                trigger OnAction()
                begin
                    IpekProductionManagement.CreatePackageFromProdOrderLine(Rec, Enum::"Package Creation Method IPK"::Single);
                end;
            }
            action("LotNos IPK")
            {
                ApplicationArea = All;
                Caption = 'Lot Nos';
                Image = LotInfo;
                RunObject = page "Prod.Order Line - Lot No. IPK";
                RunPageLink = Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Prod. Order Line No." = field("Line No.");
                ToolTip = 'Executes the Lot Nos action.';
            }
            action("Packages IPK")
            {
                ApplicationArea = All;
                Image = ShipmentLines;
                Caption = 'Packages';
                RunObject = page "Prod. Order Line Details IPK";
                RunPageLink = Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Prod. Order Line No." = field("Line No.");
                ToolTip = 'Executes the Packages action.';
            }
            action("ShowAdditionalConsumptions IPK")
            {
                ApplicationArea = All;
                Caption = 'Show Additional Consumptions', Comment = 'TRK="Ek Tüketimleri Görüntüle"';
                Image = ConsumptionJournal;
                RunObject = page "Consumption Differences IPK";
                RunPageLink = Status = field(Status), "Prod. Order No." = field("Prod. Order No.");
                ToolTip = 'Executes the Show Additional Consumptions action.';
            }
        }
    }
    var
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
}