table 60024 "Inventory Adjustment Header"
{
    Caption = 'Inventory Adjustment Header';
    DataClassification = CustomerContent;
    DrillDownPageId = "Inventory Adjustment List IPK";
    LookupPageId = "Inventory Adjustment List IPK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; Barcode; Code[50])
        {
            Caption = 'Barcode';
            Editable = true;

            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            var
                PackageNoInfoLine: Record "Package No. Info. Line IPK";
                PackageNoInformation: Record "Package No. Information";
            begin
                if PackageNoInformation.Get('', '', Rec.Barcode) and PackageNoInformation."Pallet IPK" then begin
                    PackageNoInfoLine.SetRange("Item No.", '');
                    PackageNoInfoLine.SetRange("Variant Code", '');
                    PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
                    PackageNoInfoLine.FindSet();
                    repeat
                        Rec.Barcode := PackageNoInfoLine."Source Package No. IPK";
                        ProcessInventoryAdjustmentBarcode(Rec);
                    until PackageNoInfoLine.Next() = 0;

                end else
                    ProcessInventoryAdjustmentBarcode(Rec);


            end;
        }

        field(3; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            AllowInCustomizations = Always;
            TableRelation = Location.Code;
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(4; "Journal Template Name"; Code[10])
        {
            Caption = 'Journal Template Name';
            AllowInCustomizations = Always;
            TableRelation = "Item Journal Template" where(Type = const(Item));
            ToolTip = 'Specifies the value of the Journal Template Name field.';
        }
        field(5; "Journal Batch Name"; Code[10])
        {
            Caption = 'Target Batch Name';
            AllowInCustomizations = Always;
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Journal Template Name"));
            ToolTip = 'Specifies the value of the Target Batch Name field.';
        }
        field(6; "Ready For Post"; Boolean)
        {
            // FieldClass = FlowField;
            Caption = 'Pack Nos Modified';
            ToolTip = 'Specifies the value of the Ready For Post field.';
            // CalcFormula = exist("Inventory Adjustment Line IPK" where("Document No." = field("No."), JournalLineDtlCreated = const(false)))
        }
        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
        field(7; Posted; Boolean)
        {
            // FieldClass = FlowField;
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
            // CalcFormula = exist("Inventory Adjustment Line IPK" where("Document No." = field("No."), JournalLineDtlCreated = const(false)))
        }
        field(8; LastBarcodeTotalQuantity; Decimal)
        {
            Caption = 'Last Barcode Total Quantity';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Last Barcode Total Quantity field.';
        }
        field(9; "LB Total Phy. Qty."; Decimal)
        {
            Caption = 'Last Barcode Total Physical Quantity';
            DataClassification = ToBeClassified;
            ToolTip = 'Specifies the value of the Last Barcode Total Physical Quantity field.';
            trigger OnValidate()
            var
                InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
            begin
                InventoryAdjustmentLine.SetRange("Document No.", Rec."No.");
                InventoryAdjustmentLine.SetRange("Package No.", Rec.Barcode);
                InventoryAdjustmentLine.FindFirst();
                InventoryAdjustmentLine.Validate("Physical Quantity", "LB Total Phy. Qty.");
                InventoryAdjustmentLine.Modify(true);
            end;
        }
        field(10; "Read Package Quantity"; Integer)
        {
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = count("Inventory Adjustment Line IPK" where("Document No." = field("No."), "Physical Quantity" = filter('>0')));
            Caption = 'Read Package Quantity';
            ToolTip = 'Specifies the value of the Read Package Quantity field.';
        }
        field(11; Merged; Boolean)
        {
            Caption = 'Merged';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Merged field.';
        }


    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    procedure ProcessInventoryAdjustmentBarcode(var InvAdjHeader: Record "Inventory Adjustment Header")
    var
        InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
        InventoryAdjustmentLineCheck: Record "Inventory Adjustment Line IPK";
        PackageNoInformation: Record "Package No. Information";
    begin
        InventoryAdjustmentLineCheck.SetRange("Document No.", InvAdjHeader."No.");
        InventoryAdjustmentLineCheck.SetRange("Package No.", InvAdjHeader.Barcode);
        if InventoryAdjustmentLineCheck.FindFirst() then begin
            // Error('This Package %1 is already added to this adjustemnt document', InventoryAdjustmentLineCheck."Package No.");

            InventoryAdjustmentLineCheck.CalcFields("Total Quantity");
            Rec.LastBarcodeTotalQuantity := InventoryAdjustmentLineCheck."Total Quantity";
            Rec."LB Total Phy. Qty." := InventoryAdjustmentLineCheck."Physical Quantity";
            Rec.Modify(true);
            exit;
        end;

        PackageNoInformation.SetRange("Package No.", InvAdjHeader.Barcode);
#pragma warning disable AA0210
        PackageNoInformation.SetRange("Location Code IPK", InvAdjHeader."Location Code");
#pragma warning restore AA0210
        if PackageNoInformation.FindFirst() then
            if (not PackageNoInformation."Pallet IPK") then begin
                PackageNoInformation.CalcFields("Total Quantity IPK");
                InventoryAdjustmentLine.Init();
                InventoryAdjustmentLine."Document No." := Rec."No.";
                InventoryAdjustmentLine.Insert(true);

                InventoryAdjustmentLine.Validate("Location Code", PackageNoInformation."Location Code IPK");
                InventoryAdjustmentLine.Validate("Package No.", PackageNoInformation."Package No.");
                InventoryAdjustmentLine.Validate("Physical Quantity", PackageNoInformation."Total Quantity IPK");
                InventoryAdjustmentLine.Modify(true);
            end;



        PackageNoInformation.SetRange("Package No.", InvAdjHeader.Barcode);
#pragma warning disable AA0210
        PackageNoInformation.SetRange("Location Code IPK", InvAdjHeader."Location Code");
#pragma warning restore AA0210
        PackageNoInformation.FindFirst();
        PackageNoInformation.CalcFields("Total Quantity IPK");
        Rec.LastBarcodeTotalQuantity := PackageNoInformation."Total Quantity IPK";
        Rec."LB Total Phy. Qty." := PackageNoInformation."Total Quantity IPK";
        Rec.Modify(true);

        // Rec.Barcode='';
    end;



    trigger OnInsert()
    var
        IpekSetup: Record "Ipek Pamuk Setup IPK";
        NoSeriesManagement: Codeunit "No. Series";
    begin
        // if "No." = '' then begin
        //     IpekSetup.Get();
        //     IpekSetup.TestField("Package Transfer No. Series");
        //     NoSeriesManagement.InitSeries(IpekSetup."Package Transfer No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            IpekSetup.Get();
            IpekSetup.TestField("Package Combine No. Series");
            "No. Series" := IpekSetup."Package Combine No. Series";//değiştirilecek
            if NoSeriesManagement.AreRelated(IpekSetup."Package Combine No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

        // UserSetup.Get(UserId);

        // Rec."Location Filter" := UserSetup."Location Filter IPK";
    end;
}