page 60045 "Inventory Adjustment IPK"
{
    ApplicationArea = All;
    Caption = 'Inventory Adjustment';
    PageType = Document;
    SourceTable = "Inventory Adjustment Header";
    UsageCategory = Lists;
    Editable = true;
    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus IPK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }

            group(Header)
            {
                Caption = 'Header';
                ShowCaption = false;
                // Editable = not Rec.Posted;
                field("No."; Rec."No.")
                {
                    Editable = false;
                    ToolTip = 'Specifies the value of the No. field.';
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Journal Template Name"; Rec."Journal Template Name")
                {
                }
                field("Target Batch Name"; Rec."Journal Batch Name")
                {
                }
                field("Ready For Post"; Rec."Ready For Post")
                {
                    ToolTip = 'Specifies the value of the Pack Nos Modified field.';
                }
                field(Posted; Rec.Posted)
                {
                }

            }
            group("Barcode Area")
            {
                Caption = 'Barcode Area';
                ShowCaption = false;
                field(Barcode; Rec.Barcode)
                {
                    Caption = 'Barcode';
                    Editable = true;
                    QuickEntry = true;
                    trigger OnValidate()
                    begin


                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');

                    end;
                }
            }
            group("Package Detail")
            {
                Caption = 'Package Detail';
                ShowCaption = false;
                field(TotalQuantity; Rec.LastBarcodeTotalQuantity)
                {
                    Caption = 'Last Barcode Total Quantity';
                    Editable = false;
                    Enabled = false;
                }
                field(TotalPhysicalQuantity; Rec."LB Total Phy. Qty.")
                {
                    trigger OnValidate()
                    begin

                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;
                }
                field("Read Package Quantity"; Rec."Read Package Quantity")
                {
                }
            }
            part(Line; "Inventory Adjustment Subpage")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                // Editable = not Rec.Posted;
            }
        }

    }
    actions
    {
        area(Processing)
        {

            // action(FetchUnreadPackages)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Fetch Unread Packages', Comment = 'TRK="FetchUnreadPackages"';
            //     Enabled = not Rec."Ready For Post";
            //     Image = AdjustEntries;
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     PromotedOnly = true;
            //     ToolTip = 'Executes the Fetch Unread Packages action.';

            //     trigger OnAction()

            //     begin

            //         InventoryAdjustment.FetchUnreadPackages(Rec);
            //     end;
            // }

            action(FixPackageQuantities)
            {
                ApplicationArea = All;
                Caption = 'Fix Package Quantities', Comment = 'TRK="Fix Package Quantities"';
                Enabled = not Rec."Ready For Post";
                Image = AdjustEntries;
                Promoted = true;
                // Visible = false;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Fix Package Quantities action.';

                trigger OnAction()

                begin
                    InventoryAdjustment.FixPackageQuantities(Rec);

                    // Rec."Ready For Post" := true;

                    InventoryAdjustment.PostInventoryAdjustmentLines(Rec);
                end;
            }
            // action(CreateLotAdjustmentList)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Create Lot Adjustment List', Comment = 'TRK="CreateJournalLinesFromCurrentPackageLines"';
            //     // Enabled = not Rec."Ready For Post";
            //     Image = AdjustEntries;
            //     Promoted = true;
            //     // Visible = false;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     ToolTip = 'Executes the Create Lot Adjustment List action.';

            //     trigger OnAction()

            //     begin
            //         InventoryAdjustment.CreateLotAdjustmentLinesCurrentPackageLines(Rec."Location Code", Rec."No.");
            //         Message('Lot Adjustment lines created');
            //     end;
            // }
            // action(CreateLotAdjustmentList)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Create Lot Adjustment List', Comment = 'TRK="CreateJournalLinesFromCurrentPackageLines"';
            //     // Enabled = not Rec."Ready For Post";
            //     Image = AdjustEntries;
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     ToolTip = 'Executes the Create Lot Adjustment List action.';

            //     trigger OnAction()

            //     begin
            //         InventoryAdjustment.CreateLotAdjustmentLinesCurrentPackageLines(Rec."Location Code");
            //         Message('Lot Adjustment lines created');
            //     end;
            // }


            // action(CreateJournalLines)
            // {
            //     Caption = 'Create Journal Lines', Comment = 'TRK="Post Journal Lines"';
            //     // Visible = false;
            //     Enabled = Rec."Ready For Post" and not Rec.Posted;
            //     Image = AdjustEntries;
            //     Promoted = true;
            //     // Visible = false;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     ToolTip = 'Executes the Create Journal Lines action.';

            //     trigger OnAction()
            //     var
            //     // ItemJournalLineDtl: Record "Item Journal Line Dtl. IPK";
            //     // InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
            //     begin
            //         InventoryAdjustment.CreateJournalLinesFromLotAdjustmentLine(Rec);
            //         Message('Journal lines created');
            //         //InventoryAdjustment.PostInventoryAdjustmentLines(Rec);
            //     end;
            // }
            action(TransferLines)
            {
                Caption = 'Transfer Lines', Comment = 'TRK="Post Journal Lines"';
                // Visible = false;
                // Enabled = Rec."Ready For Post" and not Rec.Posted;
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                // Visible = false;
                ToolTip = 'Executes the Transfer Lines action.';

                trigger OnAction()
                var
                    InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
                    // ItemJournalLineDtl: Record "Item Journal Line Dtl. IPK";P
                    PackageTransferHeader: Record "Package Transfer Header IPK";
                begin
                    InventoryAdjustmentLine.SetLoadFields("Package No.");
                    InventoryAdjustmentLine.SetRange("Document No.", Rec."No.");
                    InventoryAdjustmentLine.SetFilter("Physical Quantity", '>0');
                    InventoryAdjustmentLine.FindSet(false);
                    PackageTransferHeader.Init();

                    PackageTransferHeader.Insert(true);
                    PackageTransferHeader.Validate("Transfer-to Code", 'SAYIMDEPO');
                    repeat

                        PackageTransferHeader.Validate(Barcode, InventoryAdjustmentLine."Package No.");
                        PackageTransferHeader.Validate("Package Transfer Type", PackageTransferHeader."Package Transfer Type"::"Inventory Adjustment");
                    until InventoryAdjustmentLine.Next() = 0;

                    PackageTransferHeader.Modify(true);
                    Page.Run(Page::"Package Transfer Order IPK", PackageTransferHeader);

                    Message('Transfer created');
                    // InventoryAdjustment.PostInventoryAdjustmentLines(Rec);
                end;
            }
            action(ClearLocation)
            {
                ApplicationArea = All;
                Caption = 'Clear Location', Comment = 'TRK="Clear Location"';
                Enabled = not Rec."Ready For Post";
                Image = AdjustEntries;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'Executes the Clear Location action.';

                trigger OnAction()
                var
                    InventoryAdjustmentLine: Record "Inventory Adjustment Line IPK";
                    ItemJournalLine: Record "Item Journal Line";
                    ItemJournal: Page "Item Journal";
                begin
                    // InventoryAdjustmentLine.SetRange("Document No.", Rec."No.");
                    // InventoryAdjustmentLine.FindSet();
                    // repeat
                    //     InventoryAdjustment.CreateInventoryAdjustmentLineDetailFromInventoryAdjustmentLine(InventoryAdjustmentLine);
                    // until InventoryAdjustmentLine.Next() = 0;
                    InventoryAdjustment.ClearRemainingInventoryAtLocation(Rec);
                    ItemJournalLine.SetRange("Journal Batch Name", Rec."Journal Batch Name");
                    ItemJournalLine.SetRange("Journal Template Name", Rec."Journal Template Name");
                    // Page.RunModal(Page::"Item Journal", ItemJournalLine);
                    Message('Line Details Created!');
                end;
            }

        }
    }


    var
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
    // TotalPhysicalQuantity: Decimal;
    // TotalQuantity: Decimal;


}